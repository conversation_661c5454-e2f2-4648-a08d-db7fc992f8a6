/* eslint-disable @typescript-eslint/no-require-imports */
/* eslint-disable import/order */
// biome-ignore lint/style/useNodejsImportProtocol: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  transpilePackages: ['common'],
  // This includes files from the monorepo base
  outputFileTracingRoot: path.join(__dirname, '../'),
  outputFileTracingIncludes: {
    '*': [
      '../node_modules/@google-cloud/tasks/**',
      'node_modules/@google-cloud/tasks/**',
      '@google-cloud/tasks/**',
      '../node_modules/path-to-regexp/**',
      'node_modules/path-to-regexp/**',
      'path-to-regexp/**',
      '../node_modules/next-api-decorators/**',
      'node_modules/next-api-decorators/**',
      'next-api-decorators/**',
      './CHANGELOG.release.md',
      '../release/CHANGELOG.release.md',
      '../web/CHANGELOG.release.md',
      '../common/CHANGELOG.release.md',
    ],
  },
  instrumentationHook: true,
  eslint: {
    dirs: ['./'],
  },
  async headers() {
    return [
      {
        // Matching all API routes
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Credentials', value: 'true' },
          {
            key: 'Access-Control-Allow-Origin',
            // Value: 'https://app.fintary.com',
            // value: 'http://localhost:3001',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET,DELETE,PATCH,POST,PUT',
          },
          { key: 'Access-Control-Allow-Headers', value: '*' },
          // { key: "Access-Control-Allow-Headers", value: "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization" },
        ],
      },
    ];
  },
  async rewrites() {
    // TODO: Remove schedule re-writes (Moved on 2/28/2024. Remove after 3/7/2024.)
    return [
      {
        source: '/api/commission_schedules',
        destination: '/api/schedules/carriers',
      },
      {
        source: '/openapi/documents/:path*',
        destination: '/api/open/documents/:path*',
      },
    ];
  },
};

module.exports = nextConfig;

// Injected content via Sentry wizard below

const { withSentryConfig } = require('@sentry/nextjs');

module.exports = withSentryConfig(
  module.exports,
  {
    // For all available options, see:
    // https://github.com/getsentry/sentry-webpack-plugin#options

    // Suppresses source map uploading logs during build
    silent: true,
    org: 'fintary',
    project: 'api',
  },
  {
    // For all available options, see:
    // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

    // Upload a larger set of source maps for prettier stack traces (increases build time)
    widenClientFileUpload: true,

    // Transpiles SDK to be compatible with IE11 (increases bundle size)
    transpileClientSDK: true,

    // Routes browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers (increases server load)
    tunnelRoute: '/monitoring',

    // Hides source maps from generated client bundles
    hideSourceMaps: true,

    // Automatically tree-shake Sentry logger statements to reduce bundle size
    disableLogger: true,
  }
);
