import { inject, injectable } from 'inversify';

import {
  isReceivableCommissionScheduleSet,
  type CommissionSchedule,
} from './types';
import type CommissionScheduleRecord from '@/persistence/commission-schedules/CommissionScheduleRecord';
import type ICommissionScheduleRepo from '@/persistence/commission-schedules/ICommissionScheduleRepo';
import { REPOSITORY_TYPES } from '@/constants';
import type { statement_data } from '@prisma/client';
import dayjs from '@/lib/dayjs';

export interface FindSchedulesByAccountInput {
  account_id: string;
}

export interface FindSchedulesByAccountOutput {
  commission_schedules: CommissionSchedule[];
}

export interface FindMatchingScheduleInput {
  statementData: statement_data;
  accountId: string;
}

@injectable()
export default class CommissionSchedulesService {
  @inject(REPOSITORY_TYPES.CommissionScheduleRepository)
  private readonly repo: ICommissionScheduleRepo;

  private mapCommissionScheduleRecord(
    record: CommissionScheduleRecord
  ): CommissionSchedule {
    const {
      str_id,
      account_id,
      uid,
      name,
      agent_grid_level,
      commission_schedule,
      carrier_company_id,
      paying_entity_company_id,
      delay,
      start_date,
      end_date,
      issue_age_start,
      issue_age_end,
      premium_min,
      premium_max,
      notes,
      product_type,
      product_name,
    } = record;

    if (!isReceivableCommissionScheduleSet(commission_schedule)) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Invalid commission schedule format.`, commission_schedule);
      throw new Error(`Invalid commission schedule format.`);
    }

    const result: CommissionSchedule = {
      str_id,
      account_id,
      uid,
      name,
      agent_grid_level,
      commission_schedule,
      carrier_company_id,
      paying_entity_company_id,
      delay,
      start_date,
      end_date,
      issue_age_start,
      issue_age_end,
      premium_min: premium_min?.toNumber(),
      premium_max: premium_max?.toNumber(),
      notes,
      product_type,
      product_name,
    };

    return result;
  }

  async findSchedulesByAccount(
    input: FindSchedulesByAccountInput
  ): Promise<FindSchedulesByAccountOutput> {
    const { account_id } = input;

    const str_ids = await this.repo.findByAccount(account_id);
    if (str_ids.length === 0) {
      return { commission_schedules: [] };
    }

    const records = await this.repo.getByStrId(str_ids);

    const commission_schedules = records.map((record) =>
      this.mapCommissionScheduleRecord(record)
    );

    return { commission_schedules };
  }

  async findMatchingSchedule(
    input: FindMatchingScheduleInput
  ): Promise<CommissionSchedule | null> {
    const { statementData, accountId } = input;
    const { commission_schedules } = await this.findSchedulesByAccount({
      account_id: accountId,
    });

    if (commission_schedules.length === 0) {
      return null;
    }

    for (const schedule of commission_schedules) {
      if (this.isScheduleMatch(schedule, statementData)) {
        return schedule;
      }
    }

    return null;
  }

  private isScheduleMatch(
    schedule: CommissionSchedule,
    statementData: statement_data
  ): boolean {
    // if (
    //   schedule.carrier_company_id &&
    //   schedule.carrier_company_id !== statementData.writing_carrier_id
    // ) {
    //   return false;
    // }
    // if (
    //   schedule.paying_entity_company_id &&
    //   schedule.paying_entity_company_id !== statementData.paying_entity_id
    // ) {
    //   return false;
    // }
    if (schedule.start_date) {
      if (
        !statementData.payment_date ||
        dayjs(statementData.payment_date).isBefore(dayjs(schedule.start_date))
      ) {
        return false;
      }
    }
    if (schedule.end_date) {
      if (
        !statementData.payment_date ||
        dayjs(statementData.payment_date).isAfter(dayjs(schedule.end_date))
      ) {
        return false;
      }
    }
    if (schedule.issue_age_start) {
      if (
        statementData.issue_age === null ||
        statementData.issue_age === undefined ||
        statementData.issue_age < schedule.issue_age_start
      ) {
        return false;
      }
    }
    if (schedule.issue_age_end) {
      if (
        statementData.issue_age === null ||
        statementData.issue_age === undefined ||
        statementData.issue_age > schedule.issue_age_end
      ) {
        return false;
      }
    }
    if (schedule.premium_min) {
      if (
        !statementData.premium_amount ||
        statementData.premium_amount.lt(schedule.premium_min)
      ) {
        return false;
      }
    }
    if (schedule.premium_max) {
      if (
        !statementData.premium_amount ||
        statementData.premium_amount.gt(schedule.premium_max)
      ) {
        return false;
      }
    }
    if (
      schedule.product_type &&
      schedule.product_type !== statementData.product_type
    ) {
      return false;
    }
    if (
      schedule.product_name &&
      schedule.product_name !== statementData.product_name
    ) {
      return false;
    }
    // if (
    //   schedule.agent_grid_level &&
    //   schedule.agent_grid_level !== (statementData as any).agent_grid_level
    // ) {
    //   return false;
    // }

    return true;
  }
}
