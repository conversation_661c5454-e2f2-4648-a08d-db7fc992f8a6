import { type statement_data, virtual_type } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { CompensationTypes } from 'common/globalTypes';
import { inject, injectable } from 'inversify';

import { prismaClient } from '@/lib/prisma';
import dayjs from '@/lib/dayjs';
import CommissionSchedulesService from '../commission-schedules/CommissionSchedulesService';
import type { ReceivableCommissionSchedule } from '../commission-schedules/types';

@injectable()
export class AdvancedCommissionService {
  @inject(CommissionSchedulesService)
  private commissionSchedulesService: CommissionSchedulesService;
  private excludeFields = [
    'agent_commissions_status',
    'agent_commissions_status2',
    'agent_commissions_v2',
    'agent_commission_payout_rate',
    'children_data',
    'created_at',
    'created_by',
    'created_proxied_by',
    'details',
    'flags',
    'id',
    'import_id',
    'is_virtual',
    'master_id',
    'parent_id',
    'processing_status',
    'reconciliation_method',
    'reconciliation_stats',
    'reconciliation_status',
    'virtual_type',
    'state',
    'str_id',
    'updated_at',
    'updated_by',
  ];

  async processStatement(
    statementData: statement_data,
    accountId: string
  ): Promise<void> {
    const schedule = await this.commissionSchedulesService.findMatchingSchedule(
      {
        statementData,
        accountId,
      }
    );

    if (!schedule) {
      return;
    }

    if (await this.hasExistingSchedules(statementData.id)) {
      return;
    }

    const scheduledLines = this.prepareScheduledLines(
      statementData,
      schedule.commission_schedule
    );
    await this.runTransaction(statementData, scheduledLines);
  }

  async hasExistingSchedules(masterId: number): Promise<boolean> {
    const existingSchedules = await prismaClient.statement_data.count({
      where: {
        master_id: masterId,
        virtual_type: virtual_type.scheduled,
      },
    });
    return existingSchedules > 0;
  }

  prepareScheduledLines(
    statementData: statement_data,
    schedule: ReceivableCommissionSchedule[]
  ): Omit<statement_data, 'id' | 'str_id' | 'created_at' | 'updated_at'>[] {
    const originalPremium = new Decimal(statementData.premium_amount);
    const originalCommission = new Decimal(statementData.commission_amount);
    const monthlyPremium = originalPremium.div(12);

    const scheduledLines = [];
    const copy = { ...statementData };
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    this.excludeFields.forEach((field) => {
      delete copy[field];
    });

    let remainingCommission = originalCommission;

    for (const scheduleItem of schedule) {
      if (remainingCommission.isZero()) {
        break;
      }

      const rate = new Decimal(scheduleItem.rate).div(100);
      let paymentAmount = originalCommission.mul(rate);

      if (
        scheduleItem.max_commission &&
        paymentAmount.gt(scheduleItem.max_commission)
      ) {
        paymentAmount = new Decimal(scheduleItem.max_commission);
      }

      if (paymentAmount.gt(remainingCommission)) {
        paymentAmount = remainingCommission;
      }

      const paymentDate = dayjs(statementData.payment_date).add(
        Number(scheduleItem.year) - 1,
        'months'
      );

      const scheduledLine = {
        ...copy,
        virtual_type: virtual_type.scheduled,
        is_virtual: true,
        master_id: statementData.id,
        premium_amount: monthlyPremium,
        commission_amount: paymentAmount,
        payment_date: paymentDate.toDate(),
        // Set processing_date to be the same as payment_date
        processing_date: paymentDate.toDate(),
        // Set period_date to be the first day of the month of payment_date
        period_date: paymentDate.startOf('month').toDate(),
      };
      scheduledLines.push(scheduledLine);
      remainingCommission = remainingCommission.sub(paymentAmount);
    }

    return scheduledLines;
  }

  async runTransaction(
    originalStatement: statement_data,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    scheduledLines: any[]
  ) {
    await prismaClient.$transaction(async (prisma) => {
      await prisma.statement_data.update({
        where: { id: originalStatement.id },
        data: {
          compensation_type: CompensationTypes.ADVANCED,
        },
      });

      await prisma.statement_data.createMany({
        data: scheduledLines,
      });
    });
  }
}
