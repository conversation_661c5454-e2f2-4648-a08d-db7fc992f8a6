import { describe, it, expect, vi, beforeEach } from 'vitest';
import { type statement_data, virtual_type } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { CompensationTypes } from 'common/globalTypes';
import { AccountIds } from 'common/constants';

import { prismaClient } from '@/lib/prisma';
import { AdvancedCommissionService } from './index';
import dayjs from '@/lib/dayjs';
import { container } from '@/ioc';

vi.mock('@/lib/prisma', () => ({
  default: {},
  prismaClient: {
    statement_data: {
      count: vi.fn(),
      update: vi.fn(),
      createMany: vi.fn(),
      findMany: vi.fn(),
    },
    $transaction: vi.fn(),
  },
}));

const mockEligibleStatement: statement_data = {
  id: 1,
  policy_id: 'POLICY-123',
  carrier_name: 'National Life Insurance Co.',
  writing_carrier_name: 'National Life Insurance Co.',
  payment_mode: 'Monthly',
  transaction_type: 'Annualized',
  premium_amount: new Decimal(1200),
  commission_amount: new Decimal(900),
  processing_date: new Date('2023-01-15'),
  payment_date: new Date('2023-04-15'),
  str_id: 'stmt-1',
  account_id: AccountIds.BROKERS_ALLIANCE,
  uid: null,
  state: 'active',
  processing_status: null,
  created_at: new Date(),
  created_by: null,
  created_proxied_by: null,
  updated_at: new Date(),
  compensation_type: 'FYC',
  contacts: [],
  premium_type: 'policy',
  tags: [],
  type: null,
  virtual_type: null,
} as statement_data;

describe('CommissionScheduleService', () => {
  let service: AdvancedCommissionService;

  beforeEach(() => {
    service = container.get<AdvancedCommissionService>(
      AdvancedCommissionService
    );
    vi.resetAllMocks();
  });

  describe.skip('processStatement', () => {
    it('Should do nothing if statement is not eligible (wrong account)', async () => {
      const statement = {
        ...mockEligibleStatement,
        writing_carrier_name: 'National Life Insurance Co.',
      };
      await service.processStatement(statement, AccountIds.TRAILSTONE);
      expect(prismaClient.$transaction).not.toHaveBeenCalled();
    });

    it('Should do nothing if statement is not eligible (wrong carrier)', async () => {
      const statement = {
        ...mockEligibleStatement,
        writing_carrier_name: 'Other Carrier',
      };
      await service.processStatement(statement, AccountIds.BROKERS_ALLIANCE);
      expect(prismaClient.$transaction).not.toHaveBeenCalled();
    });

    it('Should do nothing if schedules already exist', async () => {
      // To test processStatement's check, we make the inner method return true
      const serviceSpy = vi
        .spyOn(service, 'hasExistingSchedules')
        .mockResolvedValue(true);
      await service.processStatement(
        mockEligibleStatement,
        AccountIds.BROKERS_ALLIANCE
      );
      expect(prismaClient.$transaction).not.toHaveBeenCalled();
      serviceSpy.mockRestore();
    });

    it('Should call internal methods for an eligible statement', async () => {
      const hasExistingSchedulesSpy = vi
        .spyOn(service, 'hasExistingSchedules')
        .mockResolvedValue(false);
      const prepareScheduledLinesSpy = vi
        .spyOn(service, 'prepareScheduledLines')
        .mockReturnValue([]);
      const runTransactionSpy = vi
        .spyOn(service, 'runTransaction')
        .mockResolvedValue();

      await service.processStatement(
        mockEligibleStatement,
        AccountIds.BROKERS_ALLIANCE
      );

      expect(hasExistingSchedulesSpy).toHaveBeenCalledOnce();
      expect(prepareScheduledLinesSpy).toHaveBeenCalledOnce();
      expect(runTransactionSpy).toHaveBeenCalledOnce();

      hasExistingSchedulesSpy.mockRestore();
      prepareScheduledLinesSpy.mockRestore();
      runTransactionSpy.mockRestore();
    });
  });

  describe('hasExistingSchedules', () => {
    it('Should return true if count is greater than 0', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.statement_data.count as any).mockResolvedValue(1);
      const result = await service.hasExistingSchedules(1);
      expect(result).toBe(true);
      expect(prismaClient.statement_data.count).toHaveBeenCalledWith({
        where: {
          master_id: 1,
          virtual_type: virtual_type.scheduled,
        },
      });
    });

    it('Should return false if count is 0', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.statement_data.count as any).mockResolvedValue(0);
      const result = await service.hasExistingSchedules(1);
      expect(result).toBe(false);
    });
  });

  describe.skip('prepareScheduledLines', () => {
    it('Should return 9 scheduled lines with correct calculations starting from current month', () => {
      const scheduledLines = service.prepareScheduledLines(
        mockEligibleStatement,
        []
      );
      expect(scheduledLines).toHaveLength(9);

      scheduledLines.forEach((line, index) => {
        expect(line.virtual_type).toBe(virtual_type.scheduled);
        expect(line.is_virtual).toBe(true);
        expect(line.master_id).toBe(mockEligibleStatement.id);
        expect(line.premium_amount.toString()).toBe('100');
        expect(line.commission_amount.toString()).toBe('100');

        // Payment date should start from current month (index 0) not next month
        const expectedPaymentDate = dayjs(mockEligibleStatement.payment_date)
          .add(index, 'months')
          .toDate();
        expect(line.payment_date).toEqual(expectedPaymentDate);

        // Processing date should be the same as payment date
        expect(line.processing_date).toEqual(expectedPaymentDate);

        // Period date should be the first day of the month of payment date
        const expectedPeriodDate = dayjs(mockEligibleStatement.payment_date)
          .add(index, 'months')
          .startOf('month')
          .toDate();
        expect(line.period_date).toEqual(expectedPeriodDate);
      });
    });
  });

  describe('runTransaction', () => {
    it('Should call prisma.$transaction and perform update and createMany', async () => {
      const mockUpdate = vi.fn();
      const mockCreateMany = vi.fn();
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.$transaction as any).mockImplementation(
        async (callback) => {
          await callback({
            statement_data: {
              update: mockUpdate,
              createMany: mockCreateMany,
            },
          });
        }
      );

      const scheduledLines = [{ commission_amount: '100.00' }];
      await service.runTransaction(mockEligibleStatement, scheduledLines);

      expect(prismaClient.$transaction).toHaveBeenCalledOnce();
      expect(mockUpdate).toHaveBeenCalledWith({
        where: { id: mockEligibleStatement.id },
        data: { compensation_type: CompensationTypes.ADVANCED },
      });
      expect(mockCreateMany).toHaveBeenCalledWith({
        data: scheduledLines,
      });
    });
  });
});
